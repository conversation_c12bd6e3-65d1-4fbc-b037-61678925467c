"""
Terminal Tools

Terminal command execution tools using the base tool system.

Author: Assistant
"""

import asyncio
import subprocess
import os
import shlex
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field

from base import BaseTool, ToolOutput, ToolStatus, register_tool


class CommandResult(BaseModel):
    """Result from command execution"""
    status: str = Field(..., description="Command execution status")
    return_code: int = Field(..., description="Process exit code")
    stdout: str = Field("", description="Standard output from the command")
    stderr: str = Field("", description="Standard error from the command")
    execution_time: float = Field(..., ge=0, description="Execution time in seconds")
    command: str = Field(..., description="The executed command")
    working_dir: str = Field(..., description="Working directory where command was executed")
    error_type: Optional[str] = Field(None, description="Type of error if execution failed")

    class Config:
        arbitrary_types_allowed = True
        json_encoders = {
            # Custom encoders can be added here if needed
        }

    def is_successful(self) -> bool:
        """Check if the command executed successfully"""
        return self.status == "success" and self.return_code == 0

    def has_output(self) -> bool:
        """Check if the command produced any output"""
        return bool(self.stdout.strip())

    def has_error_output(self) -> bool:
        """Check if the command produced error output"""
        return bool(self.stderr.strip())


class TerminalExecuteTool(BaseTool):
    """Tool for executing single terminal commands"""
    
    def __init__(self):
        super().__init__(namespace="terminal", name="execute")
    
    def get_description(self) -> str:
        return "Execute a single terminal command with timeout and error handling"
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "command": {
                    "type": "string",
                    "description": "The command to execute"
                },
                "working_dir": {
                    "type": "string",
                    "description": "Working directory for the command (optional)"
                },
                "env_vars": {
                    "type": "object",
                    "description": "Environment variables as key-value pairs (optional)"
                },
                "timeout": {
                    "type": "integer",
                    "description": "Timeout in seconds (default: 30)",
                    "default": 30
                },
                "shell": {
                    "type": "boolean",
                    "description": "Use shell execution (default: true)",
                    "default": True
                }
            },
            "required": ["command"]
        }
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        start_time = asyncio.get_event_loop().time()
        
        command = input_data["command"]
        working_dir = input_data.get("working_dir", os.getcwd())
        env_vars = input_data.get("env_vars", {})
        timeout = input_data.get("timeout", 30)
        shell = input_data.get("shell", True)
        
        try:
            # Prepare environment
            env = os.environ.copy()
            env.update(env_vars)
            
            # Execute command
            if shell:
                process = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=working_dir,
                    env=env
                )
            else:
                cmd_parts = shlex.split(command)
                process = await asyncio.create_subprocess_exec(
                    *cmd_parts,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=working_dir,
                    env=env
                )
            
            # Wait for completion with timeout
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
                
                execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
                
                result = CommandResult(
                    status="success" if process.returncode == 0 else "error",
                    return_code=process.returncode,
                    stdout=stdout.decode('utf-8', errors='replace'),
                    stderr=stderr.decode('utf-8', errors='replace'),
                    execution_time=execution_time,
                    command=command,
                    working_dir=working_dir
                )
                
                return ToolOutput(
                    status=ToolStatus.SUCCESS if process.returncode == 0 else ToolStatus.ERROR,
                    data=result,
                    execution_time=execution_time
                )
                
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
                
                result = CommandResult(
                    status="timeout",
                    return_code=-1,
                    stdout="",
                    stderr=f"Command timed out after {timeout} seconds",
                    execution_time=execution_time,
                    command=command,
                    working_dir=working_dir,
                    error_type="TimeoutError"
                )
                
                return ToolOutput(
                    status=ToolStatus.TIMEOUT,
                    data=result,
                    execution_time=execution_time
                )
                
        except Exception as e:
            execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
            
            result = CommandResult(
                status="error",
                return_code=-1,
                stdout="",
                stderr=f"Execution error: {str(e)}",
                execution_time=execution_time,
                command=command,
                working_dir=working_dir,
                error_type=type(e).__name__
            )
            
            return ToolOutput(
                status=ToolStatus.ERROR,
                data=result,
                error=str(e),
                execution_time=execution_time
            )


class TerminalBatchTool(BaseTool):
    """Tool for executing multiple terminal commands in sequence"""
    
    def __init__(self):
        super().__init__(namespace="terminal", name="batch")
    
    def get_description(self) -> str:
        return "Execute multiple terminal commands in sequence with optional stop-on-error"
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "commands": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "List of commands to execute"
                },
                "working_dir": {
                    "type": "string",
                    "description": "Working directory for all commands (optional)"
                },
                "env_vars": {
                    "type": "object",
                    "description": "Environment variables as key-value pairs (optional)"
                },
                "timeout": {
                    "type": "integer",
                    "description": "Timeout per command in seconds (default: 30)",
                    "default": 30
                },
                "shell": {
                    "type": "boolean",
                    "description": "Use shell execution (default: true)",
                    "default": True
                },
                "stop_on_error": {
                    "type": "boolean",
                    "description": "Stop execution on first error (default: false)",
                    "default": False
                }
            },
            "required": ["commands"]
        }
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        start_time = asyncio.get_event_loop().time()
        
        commands = input_data["commands"]
        working_dir = input_data.get("working_dir", os.getcwd())
        env_vars = input_data.get("env_vars", {})
        timeout = input_data.get("timeout", 30)
        shell = input_data.get("shell", True)
        stop_on_error = input_data.get("stop_on_error", False)
        
        # Create a terminal execute tool for individual commands
        execute_tool = TerminalExecuteTool()
        
        results = []
        failed_commands = 0
        
        for command in commands:
            cmd_input = {
                "command": command,
                "working_dir": working_dir,
                "env_vars": env_vars,
                "timeout": timeout,
                "shell": shell
            }
            
            result = await execute_tool.execute(cmd_input)
            results.append(result.data)
            
            if result.status != ToolStatus.SUCCESS:
                failed_commands += 1
                if stop_on_error:
                    break
        
        execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
        
        batch_summary = {
            "total_commands": len(commands),
            "executed": len(results),
            "successful": len(results) - failed_commands,
            "failed": failed_commands,
            "stopped_early": stop_on_error and failed_commands > 0 and len(results) < len(commands)
        }
        
        batch_result = {
            "batch_summary": batch_summary,
            "results": results
        }
        
        return ToolOutput(
            status=ToolStatus.SUCCESS if failed_commands == 0 else ToolStatus.ERROR,
            data=batch_result,
            execution_time=execution_time,
            metadata={"failed_commands": failed_commands}
        )


# Register the terminal tools
register_tool(TerminalExecuteTool())
register_tool(TerminalBatchTool())
