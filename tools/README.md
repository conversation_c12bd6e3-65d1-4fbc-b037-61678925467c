# Terminal Execution Tools

This directory contains tools for executing terminal commands that can be used programmatically by LLMs and other processes. The tools provide safe, controlled command execution with comprehensive result reporting.

## Files

- **`terminal_executor.py`** - Core terminal execution engine
- **`terminal_api.py`** - API wrapper with both sync and async interfaces
- **`README.md`** - This documentation file

## Quick Start

### For LLMs and Automated Processes

```python
from tools.terminal_api import execute_command_sync, execute_batch_sync

# Execute a single command
result = execute_command_sync("ls -la")
print(f"Status: {result.status}")
print(f"Output: {result.stdout}")

# Execute multiple commands
batch_result = execute_batch_sync([
    "echo 'Starting'",
    "date",
    "echo 'Done'"
])
print(f"Successful: {batch_result['batch_summary']['successful']}")
```

### For Async Applications

```python
from tools.terminal_executor import run_command, run_batch_commands

# Single command
result = await run_command("echo 'Hello'")

# Multiple commands
batch_result = await run_batch_commands([
    "git status",
    "git log --oneline -5"
])
```

## API Reference

### CommandResult

All command executions return a `CommandResult` object with:

- `status`: "success", "error", or "timeout"
- `return_code`: Exit code from the command
- `stdout`: Standard output
- `stderr`: Standard error output
- `execution_time`: Time taken in seconds
- `command`: The executed command
- `working_dir`: Working directory used
- `error_type`: Type of error (if any)

### Main Functions

#### `execute_command_sync(command, **options)`

Execute a single command synchronously.

**Parameters:**
- `command` (str): Command to execute
- `working_dir` (str, optional): Working directory
- `env_vars` (dict, optional): Environment variables
- `timeout` (int): Timeout in seconds (default: 30)
- `shell` (bool): Use shell execution (default: True)
- `return_json` (bool): Return JSON string instead of object

#### `execute_batch_sync(commands, **options)`

Execute multiple commands synchronously.

**Parameters:**
- `commands` (list): List of commands to execute
- `working_dir` (str, optional): Working directory
- `env_vars` (dict, optional): Environment variables
- `timeout` (int): Timeout per command (default: 30)
- `shell` (bool): Use shell execution (default: True)
- `stop_on_error` (bool): Stop on first error (default: False)
- `return_json` (bool): Return JSON string instead of dict

### Async Functions

#### `run_command(command, **options)`

Async version of single command execution.

#### `run_batch_commands(commands, **options)`

Async version of batch command execution.

## Programmatic Usage Only

These tools are designed for programmatic use only. They do not provide command-line interfaces as they are intended to be called directly by LLMs and other processes.

## Safety Features

- **Timeout Control**: Prevents hanging commands
- **Working Directory Isolation**: Commands run in specified directories
- **Environment Variable Control**: Safe environment variable handling
- **Non-shell Execution**: Option to disable shell for untrusted input
- **Error Handling**: Comprehensive error reporting and recovery

## Usage Examples

### System Information Gathering

```python
# Get system info for LLM analysis
commands = [
    "uname -a",           # System info
    "python --version",   # Python version
    "df -h",             # Disk usage
    "free -h"            # Memory usage (Linux)
]

result = execute_batch_sync(commands, timeout=10)
for cmd_result in result['results']:
    print(f"{cmd_result['command']}: {cmd_result['stdout'].strip()}")
```

### Git Operations

```python
# Check git repository status
git_commands = [
    "git status --porcelain",
    "git log --oneline -5",
    "git branch --show-current"
]

result = execute_batch_sync(
    git_commands, 
    working_dir="/path/to/repo",
    stop_on_error=True
)
```

### File Operations

```python
# Safe file operations
result = execute_command_sync(
    "find . -name '*.py' | head -10",
    working_dir="/project/src",
    timeout=15
)
```

## Error Handling

The tools provide detailed error information:

```python
result = execute_command_sync("nonexistent_command")
if result.status == "error":
    print(f"Command failed: {result.stderr}")
    print(f"Exit code: {result.return_code}")
elif result.status == "timeout":
    print("Command timed out")
```

## Integration with LLMs

These tools are designed to be easily integrated with LLM workflows:

1. **Structured Output**: All results are structured for easy parsing
2. **JSON Support**: Optional JSON output for API integration
3. **Batch Processing**: Execute multiple related commands efficiently
4. **Safety Controls**: Timeouts and error handling prevent system issues
5. **Detailed Reporting**: Rich information for LLM analysis

## Best Practices

1. **Always set timeouts** for commands that might hang
2. **Use working directories** to isolate command execution
3. **Handle errors gracefully** by checking the status field
4. **Use batch execution** for related commands to improve efficiency
5. **Consider non-shell execution** for untrusted input
6. **Set appropriate environment variables** for consistent behavior

## Security Considerations

- Commands are executed with the same privileges as the calling process
- Use `shell=False` for untrusted input to prevent shell injection
- Always validate command inputs when possible
- Consider using working directory restrictions
- Monitor execution times and set appropriate timeouts
