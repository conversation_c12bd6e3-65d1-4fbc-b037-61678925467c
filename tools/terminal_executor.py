"""
Terminal Command Execution Tool

A tool for executing terminal commands that can be used by LLMs and other processes.
Provides safe, controlled command execution with comprehensive result reporting.

Author: Assistant
"""

import asyncio
import subprocess
import os
import shlex
import json
from typing import Optional, Dict, Any, List
from dataclasses import dataclass, asdict


@dataclass
class CommandResult:
    """Result from command execution"""
    status: str  # "success", "error", "timeout"
    return_code: int
    stdout: str
    stderr: str
    execution_time: float
    command: str
    working_dir: str
    error_type: Optional[str] = None


class TerminalExecutor:
    """Execute terminal commands with safety controls and detailed reporting"""
    
    def __init__(self, default_timeout: int = 30, default_working_dir: Optional[str] = None):
        """
        Initialize the terminal executor
        
        Args:
            default_timeout: Default timeout in seconds for command execution
            default_working_dir: Default working directory for commands
        """
        self.default_timeout = default_timeout
        self.default_working_dir = default_working_dir or os.getcwd()
    
    async def execute_command(
        self, 
        command: str, 
        working_dir: Optional[str] = None,
        env_vars: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        shell: bool = True,
        capture_output: bool = True
    ) -> CommandResult:
        """
        Execute a terminal command and return detailed results
        
        Args:
            command: The command to execute
            working_dir: Working directory for the command
            env_vars: Additional environment variables
            timeout: Timeout in seconds (uses default if None)
            shell: Whether to use shell for execution
            capture_output: Whether to capture output or run interactively
            
        Returns:
            CommandResult with execution details
        """
        start_time = asyncio.get_event_loop().time()
        timeout = timeout or self.default_timeout
        cwd = working_dir or self.default_working_dir
        
        try:
            # Prepare environment
            env = os.environ.copy()
            if env_vars:
                env.update(env_vars)
            
            if capture_output:
                # Run with captured output
                if shell:
                    process = await asyncio.create_subprocess_shell(
                        command,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        cwd=cwd,
                        env=env
                    )
                else:
                    # Split command for non-shell execution
                    cmd_parts = shlex.split(command)
                    process = await asyncio.create_subprocess_exec(
                        *cmd_parts,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE,
                        cwd=cwd,
                        env=env
                    )
                
                # Wait for completion with timeout
                try:
                    stdout, stderr = await asyncio.wait_for(
                        process.communicate(), 
                        timeout=timeout
                    )
                    
                    execution_time = asyncio.get_event_loop().time() - start_time
                    
                    return CommandResult(
                        status="success" if process.returncode == 0 else "error",
                        return_code=process.returncode,
                        stdout=stdout.decode('utf-8', errors='replace'),
                        stderr=stderr.decode('utf-8', errors='replace'),
                        execution_time=round(execution_time, 3),
                        command=command,
                        working_dir=cwd
                    )
                    
                except asyncio.TimeoutError:
                    process.kill()
                    await process.wait()
                    return CommandResult(
                        status="timeout",
                        return_code=-1,
                        stdout="",
                        stderr=f"Command timed out after {timeout} seconds",
                        execution_time=timeout,
                        command=command,
                        working_dir=cwd,
                        error_type="TimeoutError"
                    )
            else:
                # Run interactively (no output capture)
                if shell:
                    process = await asyncio.create_subprocess_shell(
                        command,
                        cwd=cwd,
                        env=env
                    )
                else:
                    cmd_parts = shlex.split(command)
                    process = await asyncio.create_subprocess_exec(
                        *cmd_parts,
                        cwd=cwd,
                        env=env
                    )
                
                return_code = await process.wait()
                execution_time = asyncio.get_event_loop().time() - start_time
                
                return CommandResult(
                    status="success" if return_code == 0 else "error",
                    return_code=return_code,
                    stdout="(interactive mode - output not captured)",
                    stderr="",
                    execution_time=round(execution_time, 3),
                    command=command,
                    working_dir=cwd
                )
                
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            return CommandResult(
                status="error",
                return_code=-1,
                stdout="",
                stderr=f"Execution error: {str(e)}",
                execution_time=round(execution_time, 3),
                command=command,
                working_dir=cwd,
                error_type=type(e).__name__
            )
    
    async def execute_batch(
        self,
        commands: List[str],
        working_dir: Optional[str] = None,
        env_vars: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        shell: bool = True,
        stop_on_error: bool = False
    ) -> Dict[str, Any]:
        """
        Execute multiple commands in sequence
        
        Args:
            commands: List of commands to execute
            working_dir: Working directory for all commands
            env_vars: Additional environment variables
            timeout: Timeout per command in seconds
            shell: Whether to use shell for execution
            stop_on_error: Stop execution if any command fails
            
        Returns:
            Dictionary with batch execution results
        """
        results = []
        failed_commands = 0
        
        for i, command in enumerate(commands):
            result = await self.execute_command(
                command=command,
                working_dir=working_dir,
                env_vars=env_vars,
                timeout=timeout,
                shell=shell,
                capture_output=True
            )
            
            results.append(asdict(result))
            
            if result.status != "success":
                failed_commands += 1
                
                if stop_on_error:
                    break
        
        return {
            "batch_summary": {
                "total_commands": len(commands),
                "executed": len(results),
                "successful": len(results) - failed_commands,
                "failed": failed_commands,
                "stopped_early": stop_on_error and failed_commands > 0 and len(results) < len(commands)
            },
            "results": results
        }
    
    def to_json(self, result: CommandResult) -> str:
        """Convert CommandResult to JSON string"""
        return json.dumps(asdict(result), indent=2)


# Convenience functions for direct usage
async def run_command(
    command: str,
    working_dir: Optional[str] = None,
    env_vars: Optional[Dict[str, str]] = None,
    timeout: int = 30,
    shell: bool = True
) -> CommandResult:
    """
    Convenience function to run a single command
    
    Returns:
        CommandResult object
    """
    executor = TerminalExecutor(default_timeout=timeout, default_working_dir=working_dir)
    return await executor.execute_command(
        command=command,
        working_dir=working_dir,
        env_vars=env_vars,
        timeout=timeout,
        shell=shell
    )


async def run_batch_commands(
    commands: List[str],
    working_dir: Optional[str] = None,
    env_vars: Optional[Dict[str, str]] = None,
    timeout: int = 30,
    shell: bool = True,
    stop_on_error: bool = False
) -> Dict[str, Any]:
    """
    Convenience function to run multiple commands
    
    Returns:
        Dictionary with batch results
    """
    executor = TerminalExecutor(default_timeout=timeout, default_working_dir=working_dir)
    return await executor.execute_batch(
        commands=commands,
        working_dir=working_dir,
        env_vars=env_vars,
        timeout=timeout,
        shell=shell,
        stop_on_error=stop_on_error
    )


if __name__ == "__main__":
    # Example usage for testing
    async def test_executor():
        executor = TerminalExecutor()
        
        # Test single command
        result = await executor.execute_command("echo 'Hello, World!'")
        print("Single command result:")
        print(executor.to_json(result))
        
        # Test batch commands
        batch_result = await executor.execute_batch([
            "echo 'Command 1'",
            "date",
            "pwd"
        ])
        print("\nBatch command result:")
        print(json.dumps(batch_result, indent=2))
    
    asyncio.run(test_executor())
