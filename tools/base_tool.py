"""
Base Tool System

Provides the foundation for all tools in the system with namespacing and unified execution.

Author: Assistant
"""

import asyncio
import json
from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Union, List
from dataclasses import dataclass, asdict
from enum import Enum


class ToolStatus(Enum):
    """Tool execution status"""
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"


@dataclass
class ToolInput:
    """Base input structure for all tools"""
    pass


@dataclass
class ToolOutput:
    """Base output structure for all tools"""
    status: ToolStatus
    data: Any = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = asdict(self)
        result['status'] = self.status.value
        return result

    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), indent=2)


class BaseTool(ABC):
    """
    Base class for all tools in the system
    
    All tools must inherit from this class and implement the required methods.
    """
    
    def __init__(self, namespace: str, name: str):
        """
        Initialize the tool
        
        Args:
            namespace: The namespace this tool belongs to (e.g., 'terminal', 'file', 'git')
            name: The name of the tool within the namespace
        """
        self.namespace = namespace
        self.name = name
        self.full_name = f"{namespace}.{name}"
    
    @abstractmethod
    def get_description(self) -> str:
        """Return a description of what this tool does"""
        pass
    
    @abstractmethod
    def get_input_schema(self) -> Dict[str, Any]:
        """
        Return the JSON schema for the tool's input
        
        This should describe the expected input parameters and their types.
        """
        pass
    
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        """
        Execute the tool with the given input
        
        Args:
            input_data: Dictionary containing the input parameters
            
        Returns:
            ToolOutput with the execution results
        """
        pass
    
    def get_tool_definition(self) -> Dict[str, Any]:
        """
        Get the complete tool definition for registration
        
        Returns:
            Dictionary with tool metadata and schema
        """
        return {
            "namespace": self.namespace,
            "name": self.name,
            "full_name": self.full_name,
            "description": self.get_description(),
            "input_schema": self.get_input_schema()
        }
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        Validate input against the schema (basic implementation)
        
        Args:
            input_data: Input data to validate
            
        Returns:
            True if valid, False otherwise
        """
        schema = self.get_input_schema()
        required_fields = schema.get("required", [])
        
        # Check required fields are present
        for field in required_fields:
            if field not in input_data:
                return False
        
        return True


class ToolRegistry:
    """
    Registry for managing all tools in the system
    
    Provides a single entry point for tool discovery and execution.
    """
    
    def __init__(self):
        self._tools: Dict[str, BaseTool] = {}
        self._namespaces: Dict[str, List[str]] = {}
    
    def register_tool(self, tool: BaseTool) -> None:
        """
        Register a tool in the registry
        
        Args:
            tool: The tool instance to register
        """
        self._tools[tool.full_name] = tool
        
        # Update namespace tracking
        if tool.namespace not in self._namespaces:
            self._namespaces[tool.namespace] = []
        
        if tool.name not in self._namespaces[tool.namespace]:
            self._namespaces[tool.namespace].append(tool.name)
    
    def get_tool(self, full_name: str) -> Optional[BaseTool]:
        """
        Get a tool by its full name (namespace.name)
        
        Args:
            full_name: The full name of the tool (e.g., 'terminal.execute')
            
        Returns:
            The tool instance or None if not found
        """
        return self._tools.get(full_name)
    
    def list_tools(self, namespace: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List all registered tools or tools in a specific namespace
        
        Args:
            namespace: Optional namespace to filter by
            
        Returns:
            List of tool definitions
        """
        if namespace:
            tools = [tool for tool in self._tools.values() if tool.namespace == namespace]
        else:
            tools = list(self._tools.values())
        
        return [tool.get_tool_definition() for tool in tools]
    
    def list_namespaces(self) -> List[str]:
        """
        List all available namespaces
        
        Returns:
            List of namespace names
        """
        return list(self._namespaces.keys())
    
    def get_namespace_tools(self, namespace: str) -> List[str]:
        """
        Get all tool names in a specific namespace
        
        Args:
            namespace: The namespace to query
            
        Returns:
            List of tool names in the namespace
        """
        return self._namespaces.get(namespace, [])
    
    async def execute_tool(
        self, 
        full_name: str, 
        input_data: Dict[str, Any]
    ) -> ToolOutput:
        """
        Execute a tool by its full name
        
        Args:
            full_name: The full name of the tool (namespace.name)
            input_data: Input data for the tool
            
        Returns:
            ToolOutput with execution results
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            tool = self.get_tool(full_name)
            if not tool:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Tool '{full_name}' not found",
                    execution_time=0.0
                )
            
            # Validate input
            if not tool.validate_input(input_data):
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Invalid input for tool '{full_name}'",
                    execution_time=0.0
                )
            
            # Execute the tool
            result = await tool.execute(input_data)
            
            # Add execution time if not already set
            if result.execution_time is None:
                result.execution_time = round(
                    asyncio.get_event_loop().time() - start_time, 3
                )
            
            return result
            
        except Exception as e:
            execution_time = round(asyncio.get_event_loop().time() - start_time, 3)
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Tool execution failed: {str(e)}",
                execution_time=execution_time,
                metadata={"error_type": type(e).__name__}
            )


# Global tool registry instance
tool_registry = ToolRegistry()


# Convenience functions for external use
def register_tool(tool: BaseTool) -> None:
    """Register a tool in the global registry"""
    tool_registry.register_tool(tool)


def list_tools(namespace: Optional[str] = None) -> List[Dict[str, Any]]:
    """List all tools or tools in a specific namespace"""
    return tool_registry.list_tools(namespace)


def list_namespaces() -> List[str]:
    """List all available namespaces"""
    return tool_registry.list_namespaces()


async def execute_tool(full_name: str, input_data: Dict[str, Any]) -> ToolOutput:
    """Execute a tool by its full name"""
    return await tool_registry.execute_tool(full_name, input_data)


def execute_tool_sync(full_name: str, input_data: Dict[str, Any]) -> ToolOutput:
    """Execute a tool synchronously"""
    return asyncio.run(execute_tool(full_name, input_data))


def get_tool_info(full_name: str) -> Optional[Dict[str, Any]]:
    """Get information about a specific tool"""
    tool = tool_registry.get_tool(full_name)
    return tool.get_tool_definition() if tool else None
