"""
Terminal API

A simple API interface for the terminal executor that can be used by LLMs and other processes.
Provides both synchronous and asynchronous interfaces.

Author: Assistant
"""

import asyncio
import json
import sys
from typing import Optional, Dict, Any, List, Union
from pathlib import Path

# Add the tools directory to the path so we can import terminal_executor
sys.path.append(str(Path(__file__).parent))

from terminal_executor import TerminalExecutor, CommandResult, run_command, run_batch_commands


class TerminalAPI:
    """
    API interface for terminal command execution
    Provides both sync and async methods for different use cases
    """
    
    def __init__(self, default_timeout: int = 30, default_working_dir: Optional[str] = None):
        self.executor = TerminalExecutor(
            default_timeout=default_timeout,
            default_working_dir=default_working_dir
        )
    
    # Async methods (recommended for most use cases)
    async def execute(
        self,
        command: str,
        working_dir: Optional[str] = None,
        env_vars: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        shell: bool = True,
        return_json: bool = False
    ) -> Union[CommandResult, str]:
        """
        Execute a single command asynchronously
        
        Args:
            command: Command to execute
            working_dir: Working directory
            env_vars: Environment variables
            timeout: Timeout in seconds
            shell: Use shell execution
            return_json: Return JSON string instead of CommandResult object
            
        Returns:
            CommandResult object or JSON string
        """
        result = await self.executor.execute_command(
            command=command,
            working_dir=working_dir,
            env_vars=env_vars,
            timeout=timeout,
            shell=shell
        )
        
        return self.executor.to_json(result) if return_json else result
    
    async def execute_batch(
        self,
        commands: List[str],
        working_dir: Optional[str] = None,
        env_vars: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        shell: bool = True,
        stop_on_error: bool = False,
        return_json: bool = False
    ) -> Union[Dict[str, Any], str]:
        """
        Execute multiple commands asynchronously
        
        Args:
            commands: List of commands to execute
            working_dir: Working directory
            env_vars: Environment variables
            timeout: Timeout per command
            shell: Use shell execution
            stop_on_error: Stop on first error
            return_json: Return JSON string instead of dict
            
        Returns:
            Dictionary with results or JSON string
        """
        result = await self.executor.execute_batch(
            commands=commands,
            working_dir=working_dir,
            env_vars=env_vars,
            timeout=timeout,
            shell=shell,
            stop_on_error=stop_on_error
        )
        
        return json.dumps(result, indent=2) if return_json else result
    
    # Synchronous methods (for compatibility)
    def execute_sync(
        self,
        command: str,
        working_dir: Optional[str] = None,
        env_vars: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        shell: bool = True,
        return_json: bool = False
    ) -> Union[CommandResult, str]:
        """
        Execute a single command synchronously
        
        Same parameters as execute() but runs synchronously
        """
        return asyncio.run(self.execute(
            command=command,
            working_dir=working_dir,
            env_vars=env_vars,
            timeout=timeout,
            shell=shell,
            return_json=return_json
        ))
    
    def execute_batch_sync(
        self,
        commands: List[str],
        working_dir: Optional[str] = None,
        env_vars: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        shell: bool = True,
        stop_on_error: bool = False,
        return_json: bool = False
    ) -> Union[Dict[str, Any], str]:
        """
        Execute multiple commands synchronously
        
        Same parameters as execute_batch() but runs synchronously
        """
        return asyncio.run(self.execute_batch(
            commands=commands,
            working_dir=working_dir,
            env_vars=env_vars,
            timeout=timeout,
            shell=shell,
            stop_on_error=stop_on_error,
            return_json=return_json
        ))


# Convenience functions for direct usage
def execute_command_sync(
    command: str,
    working_dir: Optional[str] = None,
    env_vars: Optional[Dict[str, str]] = None,
    timeout: int = 30,
    shell: bool = True,
    return_json: bool = False
) -> Union[CommandResult, str]:
    """
    Execute a single command synchronously (convenience function)
    
    Args:
        command: Command to execute
        working_dir: Working directory
        env_vars: Environment variables as dict
        timeout: Timeout in seconds
        shell: Use shell execution
        return_json: Return JSON string instead of CommandResult
        
    Returns:
        CommandResult object or JSON string
    """
    api = TerminalAPI()
    return api.execute_sync(
        command=command,
        working_dir=working_dir,
        env_vars=env_vars,
        timeout=timeout,
        shell=shell,
        return_json=return_json
    )


def execute_batch_sync(
    commands: List[str],
    working_dir: Optional[str] = None,
    env_vars: Optional[Dict[str, str]] = None,
    timeout: int = 30,
    shell: bool = True,
    stop_on_error: bool = False,
    return_json: bool = False
) -> Union[Dict[str, Any], str]:
    """
    Execute multiple commands synchronously (convenience function)
    
    Args:
        commands: List of commands to execute
        working_dir: Working directory
        env_vars: Environment variables as dict
        timeout: Timeout per command
        shell: Use shell execution
        stop_on_error: Stop on first error
        return_json: Return JSON string instead of dict
        
    Returns:
        Dictionary with results or JSON string
    """
    api = TerminalAPI()
    return api.execute_batch_sync(
        commands=commands,
        working_dir=working_dir,
        env_vars=env_vars,
        timeout=timeout,
        shell=shell,
        stop_on_error=stop_on_error,
        return_json=return_json
    )


# Command-line interface for external processes
def main():
    """
    Command-line interface for external processes to use the terminal API
    
    Usage:
        python terminal_api.py single "command" [--working-dir DIR] [--timeout SECONDS] [--json]
        python terminal_api.py batch "cmd1" "cmd2" "cmd3" [--working-dir DIR] [--timeout SECONDS] [--json] [--stop-on-error]
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="Terminal Command Execution API")
    subparsers = parser.add_subparsers(dest='mode', help='Execution mode')
    
    # Single command parser
    single_parser = subparsers.add_parser('single', help='Execute a single command')
    single_parser.add_argument('command', help='Command to execute')
    single_parser.add_argument('--working-dir', help='Working directory')
    single_parser.add_argument('--timeout', type=int, default=30, help='Timeout in seconds')
    single_parser.add_argument('--json', action='store_true', help='Return JSON output')
    single_parser.add_argument('--no-shell', action='store_true', help='Disable shell execution')
    
    # Batch command parser
    batch_parser = subparsers.add_parser('batch', help='Execute multiple commands')
    batch_parser.add_argument('commands', nargs='+', help='Commands to execute')
    batch_parser.add_argument('--working-dir', help='Working directory')
    batch_parser.add_argument('--timeout', type=int, default=30, help='Timeout per command')
    batch_parser.add_argument('--json', action='store_true', help='Return JSON output')
    batch_parser.add_argument('--no-shell', action='store_true', help='Disable shell execution')
    batch_parser.add_argument('--stop-on-error', action='store_true', help='Stop on first error')
    
    args = parser.parse_args()
    
    if not args.mode:
        parser.print_help()
        return
    
    try:
        if args.mode == 'single':
            result = execute_command_sync(
                command=args.command,
                working_dir=args.working_dir,
                timeout=args.timeout,
                shell=not args.no_shell,
                return_json=args.json
            )
            
            if args.json:
                print(result)
            else:
                print(f"Status: {result.status}")
                print(f"Return Code: {result.return_code}")
                print(f"Execution Time: {result.execution_time}s")
                if result.stdout:
                    print(f"STDOUT:\n{result.stdout}")
                if result.stderr:
                    print(f"STDERR:\n{result.stderr}")
        
        elif args.mode == 'batch':
            result = execute_batch_sync(
                commands=args.commands,
                working_dir=args.working_dir,
                timeout=args.timeout,
                shell=not args.no_shell,
                stop_on_error=args.stop_on_error,
                return_json=args.json
            )
            
            if args.json:
                print(result)
            else:
                summary = result['batch_summary']
                print(f"Batch Summary:")
                print(f"  Total: {summary['total_commands']}")
                print(f"  Executed: {summary['executed']}")
                print(f"  Successful: {summary['successful']}")
                print(f"  Failed: {summary['failed']}")
                
                for i, cmd_result in enumerate(result['results'], 1):
                    print(f"\n[{i}] {cmd_result['command']}")
                    print(f"    Status: {cmd_result['status']} (exit: {cmd_result['return_code']})")
                    if cmd_result['stdout']:
                        print(f"    STDOUT: {cmd_result['stdout'][:100]}...")
    
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
