import asyncio
from typing import Op<PERSON>
from contextlib import AsyncExitStack

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

from dotenv import load_dotenv

load_dotenv()  # load environment variables from .env


class MCPClient:
    def __init__(self):
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()

    # methods will go here

    async def connect_to_server(self, server_script_path: str):
        """Connect to an MCP server

        Args:
            server_script_path: Path to the server script (.py or .js)
        """
        is_python = server_script_path.endswith('.py')
        is_js = server_script_path.endswith('.js')
        if not (is_python or is_js):
            raise ValueError("Server script must be a .py or .js file")

        command = "python" if is_python else "node"
        server_params = StdioServerParameters(
            command=command,
            args=[server_script_path],
            env=None
        )

        stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
        self.stdio, self.write = stdio_transport
        self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))

        await self.session.initialize()

        # List available tools
        response = await self.session.list_tools()
        tools = response.tools
        print("\nConnected to server with tools:", [tool.name for tool in tools])

    async def process_query(self, query: str) -> str:
        """Process a query by logging available tools and the query"""
        print(f"\nProcessing query: {query}")

        response = await self.session.list_tools()
        available_tools = [{
            "name": tool.name,
            "description": tool.description,
            "input_schema": tool.inputSchema
        } for tool in response.tools]

        print(available_tools)
        # print(f"Available tools: {[tool['name'] for tool in available_tools]}")
        # print(available_tools[3]['input_schema'])
        # await self.run_get_forecast()
        await self.run_script()

        return f"Query processed: {query}"
    #
    # async def run_get_forecast(self):
    #     """Run the get_alerts tool and log the result"""
    #     try:
    #         print("\nCalling get_alerts tool...")
    #         result = await self.session.call_tool("get_forecast", {
    #             'latitude': 53.8070676,
    #             'longitude': -1.5540383
    #         })
    #         print(f"get_alerts result: {result.content}")
    #         return result.content
    #     except Exception as e:
    #         print(f"Error calling get_alerts: {str(e)}")
    #         return None

    async def chat_loop(self):
        """Process a single query"""
        response = await self.process_query("test query")
        print("\n" + response)

    async def cleanup(self):
        """Clean up resources"""
        await self.exit_stack.aclose()

    async def run_script(self):
        try:
            # print("\nCalling get_alerts tool...")
            # result = await self.session.call_tool("run_custom_script", {
            #     "script_content": "ls -la"
            # })
            # print(f"get_alerts result: {result.content}")
            result = await self.session.call_tool("github_tool.clone_template", {
                "template_url": "**************:Josephmaclean/complaints.git",
                "destination_path": "./complaints"
            })
            return result.content
        except Exception as e:
            print(f"Error calling get_alerts: {str(e)}")
            return None


async def main():
    if len(sys.argv) < 2:
        print("Usage: python client.py <path_to_server_script>")
        sys.exit(1)

    client = MCPClient()
    try:
        await client.connect_to_server(sys.argv[1])
        await client.chat_loop()
    finally:
        await client.cleanup()


if __name__ == "__main__":
    import sys

    asyncio.run(main())