"""
Example usage of the Terminal Executor tools

This file demonstrates how LLMs and other processes can use the terminal execution tools.

Author: Assistant
"""

import asyncio
import json
from terminal_executor import TerminalExecutor, run_command, run_batch_commands
from terminal_api import TerminalAPI, execute_command_sync, execute_batch_sync


async def async_examples():
    """Examples using async functions"""
    print("=== Async Examples ===\n")
    
    # Example 1: Simple command execution
    print("1. Simple command execution:")
    result = await run_command("echo 'Hello from async!'")
    print(f"   Status: {result.status}")
    print(f"   Output: {result.stdout.strip()}")
    print(f"   Time: {result.execution_time}s\n")
    
    # Example 2: Command with environment variables
    print("2. Command with environment variables:")
    result = await run_command(
        'echo "My name is $USER_NAME"',
        env_vars={"USER_NAME": "Assistant"}
    )
    print(f"   Output: {result.stdout.strip()}\n")
    
    # Example 3: Batch command execution
    print("3. Batch command execution:")
    batch_result = await run_batch_commands([
        "echo 'Starting batch'",
        "date",
        "echo 'Batch complete'"
    ])
    
    summary = batch_result['batch_summary']
    print(f"   Executed: {summary['executed']}/{summary['total_commands']}")
    print(f"   Successful: {summary['successful']}")
    print("   Command outputs:")
    for i, cmd_result in enumerate(batch_result['results'], 1):
        print(f"     [{i}] {cmd_result['stdout'].strip()}")
    print()
    
    # Example 4: Using TerminalExecutor class directly
    print("4. Using TerminalExecutor class:")
    executor = TerminalExecutor(default_timeout=10)
    result = await executor.execute_command("ls -la | head -5")
    print(f"   Found {len(result.stdout.splitlines())} lines of output")
    print(f"   First line: {result.stdout.splitlines()[0] if result.stdout.splitlines() else 'No output'}\n")
    
    # Example 5: Error handling
    print("5. Error handling:")
    result = await run_command("nonexistent_command_12345")
    print(f"   Status: {result.status}")
    print(f"   Return code: {result.return_code}")
    print(f"   Error: {result.stderr.strip()}\n")


def sync_examples():
    """Examples using synchronous functions"""
    print("=== Synchronous Examples ===\n")
    
    # Example 1: Simple sync execution
    print("1. Simple sync execution:")
    result = execute_command_sync("echo 'Hello from sync!'")
    print(f"   Status: {result.status}")
    print(f"   Output: {result.stdout.strip()}\n")
    
    # Example 2: Batch sync execution
    print("2. Batch sync execution:")
    result = execute_batch_sync([
        "echo 'Sync batch 1'",
        "echo 'Sync batch 2'"
    ])
    
    summary = result['batch_summary']
    print(f"   Executed: {summary['executed']}/{summary['total_commands']}")
    for i, cmd_result in enumerate(result['results'], 1):
        print(f"     [{i}] {cmd_result['stdout'].strip()}")
    print()
    
    # Example 3: JSON output
    print("3. JSON output:")
    json_result = execute_command_sync("echo 'JSON test'", return_json=True)
    parsed = json.loads(json_result)
    print(f"   Command: {parsed['command']}")
    print(f"   Status: {parsed['status']}")
    print(f"   Output: {parsed['stdout'].strip()}\n")


def api_examples():
    """Examples using the TerminalAPI class"""
    print("=== API Examples ===\n")
    
    # Example 1: Using TerminalAPI
    print("1. Using TerminalAPI:")
    api = TerminalAPI(default_timeout=15)
    result = api.execute_sync("echo 'API test'")
    print(f"   Output: {result.stdout.strip()}\n")
    
    # Example 2: Working directory
    print("2. Working directory example:")
    result = api.execute_sync("pwd", working_dir="/tmp")
    print(f"   Working dir: {result.stdout.strip()}\n")
    
    # Example 3: Timeout test
    print("3. Timeout test (should complete quickly):")
    result = api.execute_sync("sleep 1", timeout=5)
    print(f"   Status: {result.status}")
    print(f"   Time: {result.execution_time}s\n")


def llm_integration_example():
    """Example of how an LLM might use these tools"""
    print("=== LLM Integration Example ===\n")
    
    # Simulate an LLM request to check system info
    print("LLM Request: 'Check the current system and Python version'")
    
    commands = [
        "uname -a",
        "python --version",
        "which python"
    ]
    
    result = execute_batch_sync(
        commands=commands,
        timeout=10,
        return_json=False
    )
    
    print("LLM Response:")
    print("I've checked your system information:")
    
    for i, cmd_result in enumerate(result['results']):
        cmd = cmd_result['command']
        output = cmd_result['stdout'].strip()
        
        if 'uname' in cmd:
            print(f"- System: {output}")
        elif 'python --version' in cmd:
            print(f"- Python version: {output}")
        elif 'which python' in cmd:
            print(f"- Python location: {output}")
    
    print(f"\nAll commands completed successfully: {result['batch_summary']['successful'] == len(commands)}")
    print()


def main():
    """Run all examples"""
    print("Terminal Executor Examples")
    print("=" * 50)
    
    # Run sync examples first
    sync_examples()
    api_examples()
    llm_integration_example()
    
    # Run async examples
    asyncio.run(async_examples())
    
    print("All examples completed!")


if __name__ == "__main__":
    main()
