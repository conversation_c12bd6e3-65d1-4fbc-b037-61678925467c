"""
File Tools

File system operation tools using the base tool system.

Author: Assistant
"""

import os
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional

from base_tool import <PERSON>Tool, ToolOutput, ToolStatus, register_tool


class FileReadTool(BaseTool):
    """Tool for reading file contents"""
    
    def __init__(self):
        super().__init__(namespace="file", name="read")
    
    def get_description(self) -> str:
        return "Read the contents of a file"
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "path": {
                    "type": "string",
                    "description": "Path to the file to read"
                },
                "encoding": {
                    "type": "string",
                    "description": "File encoding (default: utf-8)",
                    "default": "utf-8"
                },
                "max_size": {
                    "type": "integer",
                    "description": "Maximum file size to read in bytes (default: 1MB)",
                    "default": 1048576
                }
            },
            "required": ["path"]
        }
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        file_path = input_data["path"]
        encoding = input_data.get("encoding", "utf-8")
        max_size = input_data.get("max_size", 1048576)
        
        try:
            path = Path(file_path)
            
            if not path.exists():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"File does not exist: {file_path}"
                )
            
            if not path.is_file():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Path is not a file: {file_path}"
                )
            
            file_size = path.stat().st_size
            if file_size > max_size:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"File too large: {file_size} bytes (max: {max_size})"
                )
            
            content = path.read_text(encoding=encoding)
            
            return ToolOutput(
                status=ToolStatus.SUCCESS,
                data={
                    "content": content,
                    "size": file_size,
                    "encoding": encoding,
                    "path": str(path.absolute())
                }
            )
            
        except Exception as e:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Failed to read file: {str(e)}"
            )


class FileWriteTool(BaseTool):
    """Tool for writing content to files"""
    
    def __init__(self):
        super().__init__(namespace="file", name="write")
    
    def get_description(self) -> str:
        return "Write content to a file"
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "path": {
                    "type": "string",
                    "description": "Path to the file to write"
                },
                "content": {
                    "type": "string",
                    "description": "Content to write to the file"
                },
                "encoding": {
                    "type": "string",
                    "description": "File encoding (default: utf-8)",
                    "default": "utf-8"
                },
                "create_dirs": {
                    "type": "boolean",
                    "description": "Create parent directories if they don't exist (default: false)",
                    "default": False
                },
                "overwrite": {
                    "type": "boolean",
                    "description": "Overwrite file if it exists (default: false)",
                    "default": False
                }
            },
            "required": ["path", "content"]
        }
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        file_path = input_data["path"]
        content = input_data["content"]
        encoding = input_data.get("encoding", "utf-8")
        create_dirs = input_data.get("create_dirs", False)
        overwrite = input_data.get("overwrite", False)
        
        try:
            path = Path(file_path)
            
            # Check if file exists and overwrite is not allowed
            if path.exists() and not overwrite:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"File already exists and overwrite is disabled: {file_path}"
                )
            
            # Create parent directories if requested
            if create_dirs:
                path.parent.mkdir(parents=True, exist_ok=True)
            elif not path.parent.exists():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Parent directory does not exist: {path.parent}"
                )
            
            # Write the content
            path.write_text(content, encoding=encoding)
            
            return ToolOutput(
                status=ToolStatus.SUCCESS,
                data={
                    "path": str(path.absolute()),
                    "size": len(content.encode(encoding)),
                    "encoding": encoding,
                    "created": not path.existed_before if hasattr(path, 'existed_before') else True
                }
            )
            
        except Exception as e:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Failed to write file: {str(e)}"
            )


class FileListTool(BaseTool):
    """Tool for listing directory contents"""
    
    def __init__(self):
        super().__init__(namespace="file", name="list")
    
    def get_description(self) -> str:
        return "List contents of a directory"
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "path": {
                    "type": "string",
                    "description": "Path to the directory to list (default: current directory)",
                    "default": "."
                },
                "recursive": {
                    "type": "boolean",
                    "description": "List recursively (default: false)",
                    "default": False
                },
                "include_hidden": {
                    "type": "boolean",
                    "description": "Include hidden files (default: false)",
                    "default": False
                },
                "pattern": {
                    "type": "string",
                    "description": "Glob pattern to filter files (optional)"
                }
            },
            "required": []
        }
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        dir_path = input_data.get("path", ".")
        recursive = input_data.get("recursive", False)
        include_hidden = input_data.get("include_hidden", False)
        pattern = input_data.get("pattern")
        
        try:
            path = Path(dir_path)
            
            if not path.exists():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Directory does not exist: {dir_path}"
                )
            
            if not path.is_dir():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Path is not a directory: {dir_path}"
                )
            
            # Get file list
            if recursive:
                if pattern:
                    files = list(path.rglob(pattern))
                else:
                    files = list(path.rglob("*"))
            else:
                if pattern:
                    files = list(path.glob(pattern))
                else:
                    files = list(path.iterdir())
            
            # Filter hidden files if requested
            if not include_hidden:
                files = [f for f in files if not f.name.startswith('.')]
            
            # Build file info
            file_list = []
            for file_path in sorted(files):
                try:
                    stat = file_path.stat()
                    file_info = {
                        "name": file_path.name,
                        "path": str(file_path.absolute()),
                        "relative_path": str(file_path.relative_to(path)),
                        "type": "directory" if file_path.is_dir() else "file",
                        "size": stat.st_size if file_path.is_file() else None,
                        "modified": stat.st_mtime
                    }
                    file_list.append(file_info)
                except (OSError, PermissionError):
                    # Skip files we can't access
                    continue
            
            return ToolOutput(
                status=ToolStatus.SUCCESS,
                data={
                    "directory": str(path.absolute()),
                    "file_count": len([f for f in file_list if f["type"] == "file"]),
                    "directory_count": len([f for f in file_list if f["type"] == "directory"]),
                    "total_count": len(file_list),
                    "files": file_list
                }
            )
            
        except Exception as e:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Failed to list directory: {str(e)}"
            )


class FileDeleteTool(BaseTool):
    """Tool for deleting files and directories"""
    
    def __init__(self):
        super().__init__(namespace="file", name="delete")
    
    def get_description(self) -> str:
        return "Delete a file or directory"
    
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "path": {
                    "type": "string",
                    "description": "Path to the file or directory to delete"
                },
                "recursive": {
                    "type": "boolean",
                    "description": "Delete directories recursively (default: false)",
                    "default": False
                },
                "force": {
                    "type": "boolean",
                    "description": "Force deletion without confirmation (default: false)",
                    "default": False
                }
            },
            "required": ["path"]
        }
    
    async def execute(self, input_data: Dict[str, Any]) -> ToolOutput:
        file_path = input_data["path"]
        recursive = input_data.get("recursive", False)
        force = input_data.get("force", False)
        
        try:
            path = Path(file_path)
            
            if not path.exists():
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Path does not exist: {file_path}"
                )
            
            # Safety check - don't delete important system paths
            abs_path = path.absolute()
            dangerous_paths = [Path.home(), Path("/"), Path("C:\\") if os.name == 'nt' else None]
            dangerous_paths = [p for p in dangerous_paths if p is not None]
            
            if abs_path in dangerous_paths:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Refusing to delete system path: {abs_path}"
                )
            
            if path.is_file():
                path.unlink()
                deleted_type = "file"
            elif path.is_dir():
                if not recursive and any(path.iterdir()):
                    return ToolOutput(
                        status=ToolStatus.ERROR,
                        error=f"Directory not empty and recursive=false: {file_path}"
                    )
                shutil.rmtree(path)
                deleted_type = "directory"
            else:
                return ToolOutput(
                    status=ToolStatus.ERROR,
                    error=f"Unknown path type: {file_path}"
                )
            
            return ToolOutput(
                status=ToolStatus.SUCCESS,
                data={
                    "deleted_path": str(abs_path),
                    "type": deleted_type,
                    "recursive": recursive
                }
            )
            
        except Exception as e:
            return ToolOutput(
                status=ToolStatus.ERROR,
                error=f"Failed to delete: {str(e)}"
            )


# Register the file tools
register_tool(FileReadTool())
register_tool(FileWriteTool())
register_tool(FileListTool())
register_tool(FileDeleteTool())
