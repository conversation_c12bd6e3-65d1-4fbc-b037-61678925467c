"""
Tool Manager

Single entry point for all tools in the system. Provides unified access to all registered tools
with namespace support and comprehensive tool discovery.

Author: Assistant
"""

import importlib
import os
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from base_tool import (
    tool_registry, 
    ToolOutput, 
    execute_tool, 
    execute_tool_sync,
    list_tools,
    list_namespaces,
    get_tool_info
)


class ToolManager:
    """
    Main tool manager providing unified access to all tools
    
    This is the single entry point for tool discovery and execution.
    """
    
    def __init__(self, auto_discover: bool = True):
        """
        Initialize the tool manager
        
        Args:
            auto_discover: Whether to automatically discover and load tools
        """
        self._initialized = False
        if auto_discover:
            self.discover_tools()
    
    def discover_tools(self) -> None:
        """
        Automatically discover and load all tool modules in the tools directory
        """
        tools_dir = Path(__file__).parent
        
        # Find all Python files that end with '_tools.py'
        tool_files = list(tools_dir.glob("*_tools.py"))
        
        for tool_file in tool_files:
            module_name = tool_file.stem
            try:
                # Import the module to trigger tool registration
                importlib.import_module(module_name)
            except ImportError as e:
                print(f"Warning: Could not import tool module {module_name}: {e}")
        
        self._initialized = True
    
    def get_all_tools(self) -> List[Dict[str, Any]]:
        """
        Get information about all registered tools
        
        Returns:
            List of tool definitions
        """
        return list_tools()
    
    def get_tools_by_namespace(self, namespace: str) -> List[Dict[str, Any]]:
        """
        Get all tools in a specific namespace
        
        Args:
            namespace: The namespace to query
            
        Returns:
            List of tool definitions in the namespace
        """
        return list_tools(namespace)
    
    def get_namespaces(self) -> List[str]:
        """
        Get all available namespaces
        
        Returns:
            List of namespace names
        """
        return list_namespaces()
    
    def get_tool_definition(self, full_name: str) -> Optional[Dict[str, Any]]:
        """
        Get the definition of a specific tool
        
        Args:
            full_name: The full name of the tool (namespace.name)
            
        Returns:
            Tool definition or None if not found
        """
        return get_tool_info(full_name)
    
    async def execute(self, tool_name: str, input_data: Dict[str, Any]) -> ToolOutput:
        """
        Execute a tool asynchronously
        
        Args:
            tool_name: The full name of the tool (namespace.name)
            input_data: Input data for the tool
            
        Returns:
            ToolOutput with execution results
        """
        return await execute_tool(tool_name, input_data)
    
    def execute_sync(self, tool_name: str, input_data: Dict[str, Any]) -> ToolOutput:
        """
        Execute a tool synchronously

        Args:
            tool_name: The full name of the tool (namespace.name)
            input_data: Input data for the tool

        Returns:
            ToolOutput with execution results
        """
        import asyncio
        return asyncio.run(self.execute(tool_name, input_data))
    
    def tool_exists(self, tool_name: str) -> bool:
        """
        Check if a tool exists
        
        Args:
            tool_name: The full name of the tool (namespace.name)
            
        Returns:
            True if the tool exists, False otherwise
        """
        return self.get_tool_definition(tool_name) is not None
    
    def get_namespace_summary(self) -> Dict[str, Dict[str, Any]]:
        """
        Get a summary of all namespaces and their tools
        
        Returns:
            Dictionary with namespace information
        """
        summary = {}
        
        for namespace in self.get_namespaces():
            tools = self.get_tools_by_namespace(namespace)
            summary[namespace] = {
                "tool_count": len(tools),
                "tools": [tool["name"] for tool in tools],
                "descriptions": {tool["name"]: tool["description"] for tool in tools}
            }
        
        return summary
    
    def search_tools(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for tools by name or description
        
        Args:
            query: Search query string
            
        Returns:
            List of matching tool definitions
        """
        query_lower = query.lower()
        all_tools = self.get_all_tools()
        
        matching_tools = []
        for tool in all_tools:
            # Search in name, full_name, and description
            if (query_lower in tool["name"].lower() or 
                query_lower in tool["full_name"].lower() or 
                query_lower in tool["description"].lower()):
                matching_tools.append(tool)
        
        return matching_tools


# Global tool manager instance
tool_manager = ToolManager()


# Convenience functions for external use
def get_all_tools() -> List[Dict[str, Any]]:
    """Get information about all registered tools"""
    return tool_manager.get_all_tools()


def get_tools_by_namespace(namespace: str) -> List[Dict[str, Any]]:
    """Get all tools in a specific namespace"""
    return tool_manager.get_tools_by_namespace(namespace)


def get_namespaces() -> List[str]:
    """Get all available namespaces"""
    return tool_manager.get_namespaces()


def get_tool_definition(full_name: str) -> Optional[Dict[str, Any]]:
    """Get the definition of a specific tool"""
    return tool_manager.get_tool_definition(full_name)


async def execute_tool_async(tool_name: str, input_data: Dict[str, Any]) -> ToolOutput:
    """Execute a tool asynchronously"""
    return await tool_manager.execute(tool_name, input_data)


def execute_tool_sync_direct(tool_name: str, input_data: Dict[str, Any]) -> ToolOutput:
    """Execute a tool synchronously (direct call to avoid recursion)"""
    return execute_tool_sync(tool_name, input_data)


def tool_exists(tool_name: str) -> bool:
    """Check if a tool exists"""
    return tool_manager.tool_exists(tool_name)


def get_namespace_summary() -> Dict[str, Dict[str, Any]]:
    """Get a summary of all namespaces and their tools"""
    return tool_manager.get_namespace_summary()


def search_tools(query: str) -> List[Dict[str, Any]]:
    """Search for tools by name or description"""
    return tool_manager.search_tools(query)


# Unified execution function - main entry point
def execute(tool_name: str, input_data: Dict[str, Any], async_mode: bool = False) -> Union[ToolOutput, Any]:
    """
    Main entry point for tool execution
    
    Args:
        tool_name: The full name of the tool (namespace.name)
        input_data: Input data for the tool
        async_mode: Whether to return a coroutine for async execution
        
    Returns:
        ToolOutput (sync) or coroutine (async)
    """
    if async_mode:
        return execute_tool_async(tool_name, input_data)
    else:
        return execute_tool_sync(tool_name, input_data)


# Tool discovery and information functions
def discover_tools() -> None:
    """Force rediscovery of tools"""
    tool_manager.discover_tools()


def print_tool_summary() -> None:
    """Print a summary of all available tools"""
    summary = get_namespace_summary()
    
    print("Available Tools Summary")
    print("=" * 50)
    
    for namespace, info in summary.items():
        print(f"\nNamespace: {namespace}")
        print(f"  Tools: {info['tool_count']}")
        for tool_name, description in info['descriptions'].items():
            print(f"    • {tool_name}: {description}")
    
    print(f"\nTotal namespaces: {len(summary)}")
    print(f"Total tools: {sum(info['tool_count'] for info in summary.values())}")


def print_tool_details(tool_name: str) -> None:
    """Print detailed information about a specific tool"""
    tool_def = get_tool_definition(tool_name)
    
    if not tool_def:
        print(f"Tool '{tool_name}' not found")
        return
    
    print(f"Tool: {tool_def['full_name']}")
    print(f"Description: {tool_def['description']}")
    print(f"Namespace: {tool_def['namespace']}")
    print(f"Name: {tool_def['name']}")
    print("\nInput Schema:")
    
    schema = tool_def['input_schema']
    if 'properties' in schema:
        for prop_name, prop_def in schema['properties'].items():
            required = prop_name in schema.get('required', [])
            req_str = " (required)" if required else " (optional)"
            print(f"  • {prop_name}{req_str}: {prop_def.get('description', 'No description')}")
    else:
        print("  No input parameters")
