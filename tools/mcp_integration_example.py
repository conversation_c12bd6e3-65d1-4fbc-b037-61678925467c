"""
MCP Integration Example

This example shows how the terminal execution tools can be integrated 
with MCP (Model Context Protocol) servers and used by LLMs.

Author: Assistant
"""

import asyncio
import json
from terminal_api import TerminalAPI, execute_command_sync, execute_batch_sync


class MCPTerminalTool:
    """
    Example MCP tool that provides terminal execution capabilities
    This would typically be part of an MCP server implementation
    """
    
    def __init__(self):
        self.api = TerminalAPI(default_timeout=30)
    
    def get_tool_definition(self):
        """Return the MCP tool definition for terminal execution"""
        return {
            "name": "execute_terminal_command",
            "description": "Execute terminal commands safely with timeout and error handling",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "The command to execute"
                    },
                    "working_dir": {
                        "type": "string",
                        "description": "Working directory for the command (optional)"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in seconds (default: 30)",
                        "default": 30
                    },
                    "env_vars": {
                        "type": "object",
                        "description": "Environment variables as key-value pairs (optional)"
                    },
                    "shell": {
                        "type": "boolean",
                        "description": "Use shell execution (default: true)",
                        "default": True
                    }
                },
                "required": ["command"]
            }
        }
    
    def get_batch_tool_definition(self):
        """Return the MCP tool definition for batch terminal execution"""
        return {
            "name": "execute_terminal_batch",
            "description": "Execute multiple terminal commands in sequence",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "commands": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of commands to execute"
                    },
                    "working_dir": {
                        "type": "string",
                        "description": "Working directory for all commands (optional)"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout per command in seconds (default: 30)",
                        "default": 30
                    },
                    "env_vars": {
                        "type": "object",
                        "description": "Environment variables as key-value pairs (optional)"
                    },
                    "shell": {
                        "type": "boolean",
                        "description": "Use shell execution (default: true)",
                        "default": True
                    },
                    "stop_on_error": {
                        "type": "boolean",
                        "description": "Stop execution on first error (default: false)",
                        "default": False
                    }
                },
                "required": ["commands"]
            }
        }
    
    async def execute_command_tool(self, params):
        """Execute the terminal command tool"""
        try:
            result = await self.api.execute(
                command=params["command"],
                working_dir=params.get("working_dir"),
                env_vars=params.get("env_vars"),
                timeout=params.get("timeout", 30),
                shell=params.get("shell", True)
            )
            
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Command executed: {result.command}\n"
                               f"Status: {result.status}\n"
                               f"Return code: {result.return_code}\n"
                               f"Execution time: {result.execution_time}s\n"
                               f"Working directory: {result.working_dir}\n\n"
                               f"STDOUT:\n{result.stdout}\n"
                               f"STDERR:\n{result.stderr}"
                    }
                ],
                "isError": result.status != "success"
            }
        except Exception as e:
            return {
                "content": [
                    {
                        "type": "text", 
                        "text": f"Error executing command: {str(e)}"
                    }
                ],
                "isError": True
            }
    
    async def execute_batch_tool(self, params):
        """Execute the batch terminal command tool"""
        try:
            result = await self.api.execute_batch(
                commands=params["commands"],
                working_dir=params.get("working_dir"),
                env_vars=params.get("env_vars"),
                timeout=params.get("timeout", 30),
                shell=params.get("shell", True),
                stop_on_error=params.get("stop_on_error", False)
            )
            
            summary = result["batch_summary"]
            output_text = f"Batch execution completed:\n"
            output_text += f"Total commands: {summary['total_commands']}\n"
            output_text += f"Executed: {summary['executed']}\n"
            output_text += f"Successful: {summary['successful']}\n"
            output_text += f"Failed: {summary['failed']}\n\n"
            
            for i, cmd_result in enumerate(result["results"], 1):
                output_text += f"[{i}] {cmd_result['command']}\n"
                output_text += f"    Status: {cmd_result['status']} (exit: {cmd_result['return_code']})\n"
                output_text += f"    Time: {cmd_result['execution_time']}s\n"
                if cmd_result['stdout']:
                    output_text += f"    STDOUT: {cmd_result['stdout']}\n"
                if cmd_result['stderr']:
                    output_text += f"    STDERR: {cmd_result['stderr']}\n"
                output_text += "\n"
            
            return {
                "content": [
                    {
                        "type": "text",
                        "text": output_text
                    }
                ],
                "isError": summary['failed'] > 0
            }
        except Exception as e:
            return {
                "content": [
                    {
                        "type": "text",
                        "text": f"Error executing batch commands: {str(e)}"
                    }
                ],
                "isError": True
            }


def llm_workflow_example():
    """
    Example of how an LLM might use the terminal tools in a workflow
    """
    print("=== LLM Workflow Example ===\n")
    
    # Scenario: LLM needs to analyze a Python project
    print("LLM Task: Analyze a Python project structure and dependencies\n")
    
    # Step 1: Get basic project info
    print("Step 1: Getting project structure...")
    structure_commands = [
        "find . -name '*.py' | head -10",
        "ls -la",
        "cat requirements.txt 2>/dev/null || echo 'No requirements.txt found'"
    ]
    
    result = execute_batch_sync(
        structure_commands,
        timeout=10,
        stop_on_error=False
    )
    
    print("Project analysis results:")
    for i, cmd_result in enumerate(result['results']):
        cmd = cmd_result['command']
        if 'find' in cmd:
            py_files = [line for line in cmd_result['stdout'].splitlines() if line.strip()]
            print(f"- Found {len(py_files)} Python files")
        elif 'ls -la' in cmd:
            print(f"- Directory listing completed")
        elif 'requirements.txt' in cmd:
            if 'No requirements.txt' in cmd_result['stdout']:
                print("- No requirements.txt found")
            else:
                print("- Found requirements.txt")
    
    # Step 2: Check Python environment
    print("\nStep 2: Checking Python environment...")
    env_result = execute_command_sync("python --version && which python")
    print(f"- Python info: {env_result.stdout.strip()}")
    
    # Step 3: Run a simple test
    print("\nStep 3: Testing Python syntax...")
    test_result = execute_command_sync("python -m py_compile main.py")
    if test_result.status == "success":
        print("- main.py syntax is valid")
    else:
        print(f"- Syntax check failed: {test_result.stderr.strip()}")
    
    print("\nLLM Analysis Complete!")
    print(f"Total execution time: {sum(r['execution_time'] for r in result['results']) + env_result.execution_time + test_result.execution_time:.2f}s")


async def mcp_server_simulation():
    """
    Simulate how an MCP server would handle terminal tool requests
    """
    print("\n=== MCP Server Simulation ===\n")
    
    # Create the MCP terminal tool
    terminal_tool = MCPTerminalTool()
    
    # Simulate tool registration
    print("Registering terminal tools with MCP server...")
    single_tool_def = terminal_tool.get_tool_definition()
    batch_tool_def = terminal_tool.get_batch_tool_definition()
    
    print(f"- Registered: {single_tool_def['name']}")
    print(f"- Registered: {batch_tool_def['name']}")
    
    # Simulate LLM calling the single command tool
    print("\nSimulating LLM tool call: execute_terminal_command")
    single_params = {
        "command": "echo 'Hello from MCP tool!'",
        "timeout": 10
    }
    
    single_result = await terminal_tool.execute_command_tool(single_params)
    print("Tool response:")
    print(single_result['content'][0]['text'])
    
    # Simulate LLM calling the batch command tool
    print("\nSimulating LLM tool call: execute_terminal_batch")
    batch_params = {
        "commands": [
            "echo 'Batch command 1'",
            "date",
            "echo 'Batch command 2'"
        ],
        "timeout": 10
    }
    
    batch_result = await terminal_tool.execute_batch_tool(batch_params)
    print("Tool response:")
    print(batch_result['content'][0]['text'])


def main():
    """Run all examples"""
    print("Terminal Tools - MCP Integration Examples")
    print("=" * 60)
    
    # Run synchronous examples
    llm_workflow_example()
    
    # Run async MCP simulation
    asyncio.run(mcp_server_simulation())
    
    print("\nAll integration examples completed!")


if __name__ == "__main__":
    main()
